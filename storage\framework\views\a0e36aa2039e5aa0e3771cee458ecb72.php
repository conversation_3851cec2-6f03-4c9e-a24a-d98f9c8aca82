<?php $__env->startSection('title', 'Payment Successful - Escape Matrix Academy'); ?>

<?php $__env->startSection('content'); ?>
<!-- Payment Success Hero Section -->
<section class="py-20 bg-gradient-to-br from-green-900 via-gray-900 to-black">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto text-center">
            <!-- Success Icon -->
            <div class="mb-8">
                <div class="w-24 h-24 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                </div>
                <h1 class="text-4xl md:text-5xl font-bold text-white mb-6">Payment Successful!</h1>
                <p class="text-xl text-gray-300 mb-8">
                    Congratulations! Your payment has been processed successfully and you're now enrolled in the course.
                </p>
            </div>

            <!-- Payment Details Card -->
            <?php if(isset($payment) && $payment): ?>
            <div class="bg-gray-800 border border-gray-700 rounded-lg p-8 mb-8 text-left">
                <h2 class="text-2xl font-bold text-white mb-6 text-center">Payment Details</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <div>
                            <span class="text-gray-400 text-sm">Course</span>
                            <p class="text-white font-semibold"><?php echo e($payment->course->title); ?></p>
                        </div>
                        <div>
                            <span class="text-gray-400 text-sm">Amount Paid</span>
                            <p class="text-green-400 font-bold text-xl">$<?php echo e(number_format($payment->amount, 2)); ?></p>
                        </div>
                        <div>
                            <span class="text-gray-400 text-sm">Payment Method</span>
                            <p class="text-white">PayPal</p>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <div>
                            <span class="text-gray-400 text-sm">Transaction ID</span>
                            <p class="text-white font-mono text-sm"><?php echo e($payment->payment_id); ?></p>
                        </div>
                        <div>
                            <span class="text-gray-400 text-sm">Date</span>
                            <p class="text-white"><?php echo e($payment->created_at->format('M d, Y \a\t g:i A')); ?></p>
                        </div>
                        <div>
                            <span class="text-gray-400 text-sm">Status</span>
                            <span class="bg-green-600 text-white px-3 py-1 rounded-full text-sm font-medium">Completed</span>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <?php if(isset($payment) && $payment && $payment->course): ?>
                    <a href="<?php echo e(route('my-courses.view', $payment->course)); ?>" 
                       class="bg-green-600 hover:bg-green-700 text-white px-8 py-4 rounded-md font-semibold transition-colors flex items-center justify-center space-x-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                        <span>Start Learning</span>
                    </a>
                <?php endif; ?>
                
                <a href="<?php echo e(route('courses.index')); ?>" 
                   class="border border-gray-600 text-gray-300 hover:bg-gray-700 px-8 py-4 rounded-md font-semibold transition-colors flex items-center justify-center space-x-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0l-4 4m4-4l-4-4"></path>
                    </svg>
                    <span>Browse More Courses</span>
                </a>
                
                <a href="<?php echo e(route('payments.history')); ?>" 
                   class="border border-gray-600 text-gray-300 hover:bg-gray-700 px-8 py-4 rounded-md font-semibold transition-colors flex items-center justify-center space-x-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <span>Payment History</span>
                </a>
            </div>
        </div>
    </div>
</section>

<!-- What's Next Section -->
<section class="py-16 bg-gray-900">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
            <h2 class="text-3xl font-bold text-white mb-12 text-center">What's Next?</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="bg-gray-800 border border-gray-700 rounded-lg p-6 text-center">
                    <div class="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-white mb-2">Access Your Course</h3>
                    <p class="text-gray-400">Start learning immediately with lifetime access to all course materials.</p>
                </div>
                
                <div class="bg-gray-800 border border-gray-700 rounded-lg p-6 text-center">
                    <div class="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-white mb-2">Join the Community</h3>
                    <p class="text-gray-400">Connect with fellow students and get support from instructors.</p>
                </div>
                
                <div class="bg-gray-800 border border-gray-700 rounded-lg p-6 text-center">
                    <div class="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-white mb-2">Earn Your Certificate</h3>
                    <p class="text-gray-400">Complete the course and receive a certificate of completion.</p>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\00. RENATA\CODE\escapematrix\resources\views/payments/success.blade.php ENDPATH**/ ?>