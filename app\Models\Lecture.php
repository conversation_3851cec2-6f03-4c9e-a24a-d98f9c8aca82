<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Lecture extends Model
{
    use HasFactory;

    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'title',
        'slug',
        'description',
        'type',
        'chapter_id',
        'course_id',
        'instructor_id',
        'sort_order',
        'is_published',
        'is_free_preview',
        'duration_minutes',
        'content',
        'video_url',
        'video_provider',
        'video_id',
        'video_thumbnail',
        'video_metadata',
        'resources',
        'attachments',
        'quiz_data',
        'quiz_passing_score',
        'quiz_allow_retakes',
        'is_mandatory',
        'estimated_completion_minutes',
    ];

    protected $casts = [
        'sort_order' => 'integer',
        'is_published' => 'boolean',
        'is_free_preview' => 'boolean',
        'duration_minutes' => 'integer',
        'video_metadata' => 'array',
        'resources' => 'array',
        'attachments' => 'array',
        'quiz_data' => 'array',
        'quiz_passing_score' => 'integer',
        'quiz_allow_retakes' => 'boolean',
        'is_mandatory' => 'boolean',
        'estimated_completion_minutes' => 'integer',
    ];

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($model) {
            if (empty($model->id)) {
                $model->id = (string) Str::uuid();
            }
            if (empty($model->slug)) {
                $model->slug = $model->generateUniqueLectureSlug($model->title, $model->chapter_id);
            }
            if (is_null($model->sort_order)) {
                $model->sort_order = static::where('chapter_id', $model->chapter_id)->max('sort_order') + 1;
            }
        });

        static::updating(function ($model) {
            if ($model->isDirty('title') && empty($model->slug)) {
                $model->slug = $model->generateUniqueLectureSlug($model->title, $model->chapter_id, $model->id);
            }
        });

        static::saved(function ($model) {
            // Update chapter and course statistics when lecture is saved
            $model->chapter->updateStatistics();
        });

        static::deleted(function ($model) {
            // Update chapter and course statistics when lecture is deleted
            $model->chapter->updateStatistics();
        });
    }

    /**
     * Get the chapter that owns the lecture.
     */
    public function chapter(): BelongsTo
    {
        return $this->belongsTo(Chapter::class);
    }

    /**
     * Get the course that owns the lecture.
     */
    public function course(): BelongsTo
    {
        return $this->belongsTo(Course::class);
    }

    /**
     * Get the instructor that owns the lecture.
     */
    public function instructor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'instructor_id');
    }

    /**
     * Get the progress records for this lecture.
     */
    public function progress(): HasMany
    {
        return $this->hasMany(LectureProgress::class);
    }

    /**
     * Scope to filter published lectures.
     */
    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    /**
     * Scope to filter by chapter.
     */
    public function scopeByChapter($query, $chapterId)
    {
        return $query->where('chapter_id', $chapterId);
    }

    /**
     * Scope to filter by course.
     */
    public function scopeByCourse($query, $courseId)
    {
        return $query->where('course_id', $courseId);
    }

    /**
     * Scope to filter by instructor.
     */
    public function scopeByInstructor($query, $instructorId)
    {
        return $query->where('instructor_id', $instructorId);
    }

    /**
     * Scope to filter by type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    /**
     * Check if the lecture is published.
     */
    public function isPublished(): bool
    {
        return $this->is_published;
    }

    /**
     * Check if the lecture is a video.
     */
    public function isVideo(): bool
    {
        return $this->type === 'video';
    }

    /**
     * Check if the lecture is a quiz.
     */
    public function isQuiz(): bool
    {
        return $this->type === 'quiz';
    }

    /**
     * Check if the lecture is text content.
     */
    public function isText(): bool
    {
        return $this->type === 'text';
    }

    /**
     * Check if the lecture has resources.
     */
    public function hasResources(): bool
    {
        return !empty($this->resources);
    }

    /**
     * Check if the lecture has attachments.
     */
    public function hasAttachments(): bool
    {
        return !empty($this->attachments);
    }

    /**
     * Get the lecture duration in human readable format.
     */
    public function getFormattedDuration(): string
    {
        if ($this->duration_minutes < 60) {
            return $this->duration_minutes . ' minutes';
        }
        
        $hours = floor($this->duration_minutes / 60);
        $minutes = $this->duration_minutes % 60;
        
        if ($minutes === 0) {
            return $hours . ' hour' . ($hours > 1 ? 's' : '');
        }
        
        return $hours . 'h ' . $minutes . 'm';
    }

    /**
     * Get the next lecture in the chapter.
     */
    public function getNextLecture(): ?self
    {
        return static::where('chapter_id', $this->chapter_id)
            ->where('sort_order', '>', $this->sort_order)
            ->orderBy('sort_order')
            ->first();
    }

    /**
     * Get the previous lecture in the chapter.
     */
    public function getPreviousLecture(): ?self
    {
        return static::where('chapter_id', $this->chapter_id)
            ->where('sort_order', '<', $this->sort_order)
            ->orderBy('sort_order', 'desc')
            ->first();
    }

    /**
     * Get the next lecture in the entire course.
     */
    public function getNextLectureInCourse(): ?self
    {
        // First try to get next lecture in same chapter
        $nextInChapter = $this->getNextLecture();
        if ($nextInChapter) {
            return $nextInChapter;
        }

        // If no next lecture in chapter, get first lecture of next chapter
        $nextChapter = $this->chapter->getNextChapter();
        if ($nextChapter) {
            return $nextChapter->lectures()->orderBy('sort_order')->first();
        }

        return null;
    }

    /**
     * Get the previous lecture in the entire course.
     */
    public function getPreviousLectureInCourse(): ?self
    {
        // First try to get previous lecture in same chapter
        $previousInChapter = $this->getPreviousLecture();
        if ($previousInChapter) {
            return $previousInChapter;
        }

        // If no previous lecture in chapter, get last lecture of previous chapter
        $previousChapter = $this->chapter->getPreviousChapter();
        if ($previousChapter) {
            return $previousChapter->lectures()->orderBy('sort_order', 'desc')->first();
        }

        return null;
    }

    /**
     * Move lecture up in sort order.
     */
    public function moveUp(): bool
    {
        $previousLecture = $this->getPreviousLecture();
        if (!$previousLecture) {
            return false;
        }

        $tempOrder = $this->sort_order;
        $this->sort_order = $previousLecture->sort_order;
        $previousLecture->sort_order = $tempOrder;

        $this->save();
        $previousLecture->save();

        return true;
    }

    /**
     * Move lecture down in sort order.
     */
    public function moveDown(): bool
    {
        $nextLecture = $this->getNextLecture();
        if (!$nextLecture) {
            return false;
        }

        $tempOrder = $this->sort_order;
        $this->sort_order = $nextLecture->sort_order;
        $nextLecture->sort_order = $tempOrder;

        $this->save();
        $nextLecture->save();

        return true;
    }

    /**
     * Generate a unique slug for the lecture
     */
    public function generateUniqueLectureSlug($title, $chapterId, $excludeId = null)
    {
        $lectureSlug = Str::slug($title);
        $baseSlug = $lectureSlug;
        $slug = $baseSlug;
        $counter = 1;

        while (true) {
            $query = static::where('chapter_id', $chapterId)->where('slug', $slug);
            if ($excludeId) {
                $query->where('id', '!=', $excludeId);
            }

            if (!$query->exists()) {
                break;
            }

            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }
}
