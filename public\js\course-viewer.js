/**
 * Course Viewer - Professional LMS Interface JavaScript
 * Handles navigation, progress tracking, sidebar controls, and AJAX operations
 */

class CourseViewer {
    constructor() {
        this.courseId = null;
        this.currentLectureId = null;
        this.currentLectureSlug = null;
        this.currentChapterSlug = null;
        this.csrfToken = null;
        this.sidebarOpen = false;
        this.sidebarHiddenOnDesktop = false;
        this.isMobile = window.innerWidth < 1024;

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupResponsiveHandling();
        this.initializeSidebar();
        this.setupKeyboardShortcuts();
        
        // Handle browser back/forward navigation
        this.setupPopstateHandler();
        this.expandCurrentChapter();
    }

    setupEventListeners() {
        // Sidebar toggle (main content header)
        const sidebarToggle = document.querySelector('.sidebar-toggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => this.toggleSidebar());
        }

        // Sidebar hamburger toggle (sidebar header)
        const sidebarHamburgerToggle = document.querySelector('.sidebar-hamburger-toggle');
        if (sidebarHamburgerToggle) {
            sidebarHamburgerToggle.addEventListener('click', () => this.toggleSidebar());
        }

        // Sidebar close button
        const sidebarClose = document.querySelector('.sidebar-close-btn');
        if (sidebarClose) {
            sidebarClose.addEventListener('click', () => this.closeSidebar());
        }

        // Sidebar overlay
        const sidebarOverlay = document.querySelector('.sidebar-overlay');
        if (sidebarOverlay) {
            sidebarOverlay.addEventListener('click', () => this.closeSidebar());
        }

        // Chapter toggles
        document.querySelectorAll('.chapter-header').forEach(header => {
            header.addEventListener('click', (e) => this.toggleChapter(e));
        });

        // Lecture navigation
        document.querySelectorAll('.lecture-item').forEach(item => {
            item.addEventListener('click', (e) => this.handleLectureClick(e));
        });

        // Complete button
        const completeBtn = document.querySelector('.complete-btn');
        if (completeBtn) {
            completeBtn.addEventListener('click', (e) => this.toggleLectureComplete(e));
        }

        // Navigation buttons
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.handleNavigation(e));
        });
    }

    setupResponsiveHandling() {
        window.addEventListener('resize', () => {
            const wasMobile = this.isMobile;
            this.isMobile = window.innerWidth < 1024;

            // If switching from mobile to desktop, close mobile sidebar and reset desktop state
            if (wasMobile && !this.isMobile) {
                this.closeSidebar();
                this.showSidebarOnDesktop(); // Ensure sidebar is visible on desktop by default
            }

            // If switching from desktop to mobile, reset desktop sidebar state
            if (!wasMobile && this.isMobile) {
                this.showSidebarOnDesktop(); // Reset desktop classes
                this.closeSidebar(); // Close mobile sidebar by default
            }
        });
    }

    readMetaTags() {
        // Get course and lecture IDs from meta tags or data attributes
        const courseIdMeta = document.querySelector('meta[name="course-id"]');
        const lectureIdMeta = document.querySelector('meta[name="current-lecture-id"]');
        const lectureSlugMeta = document.querySelector('meta[name="current-lecture-slug"]');
        const chapterSlugMeta = document.querySelector('meta[name="current-chapter-slug"]');
        const csrfMeta = document.querySelector('meta[name="csrf-token"]');
        
        if (courseIdMeta) this.courseId = courseIdMeta.getAttribute('content');
        if (lectureIdMeta) this.currentLectureId = lectureIdMeta.getAttribute('content');
        if (lectureSlugMeta) this.currentLectureSlug = lectureSlugMeta.getAttribute('content');
        if (chapterSlugMeta) this.currentChapterSlug = chapterSlugMeta.getAttribute('content');
        if (csrfMeta) this.csrfToken = csrfMeta.getAttribute('content');
        
        // Fallback: Extract from URL if meta tags are missing
        this.extractFromUrlIfNeeded();
    }
    
    extractFromUrlIfNeeded() {
        const currentPath = window.location.pathname;
        
        // Pattern: /my-courses/{course-slug}/{chapter-slug}/lecture/{lecture-slug}
        const urlPattern = /\/my-courses\/([^/]+)\/([^/]+)\/lecture\/([^/]+)/;
        const match = currentPath.match(urlPattern);
        
        if (match) {
            const [, courseSlug, chapterSlug, lectureSlug] = match;
            
            // Only use URL values if meta tag values are missing
            if (!this.courseId) this.courseId = courseSlug;
            if (!this.currentChapterSlug) this.currentChapterSlug = chapterSlug;
            if (!this.currentLectureSlug) this.currentLectureSlug = lectureSlug;
            
            console.log('Extracted from URL:', {
                courseSlug,
                chapterSlug,
                lectureSlug
            });
        } else {
            console.warn('Could not extract course/chapter/lecture slugs from URL:', currentPath);
        }
    }

    initializeSidebar() {
        // Read meta tags to get current values
        this.readMetaTags();

        // Initialize sidebar state for mobile
        if (this.isMobile) {
            this.closeSidebar();
        }
    }

    toggleSidebar() {
        if (this.isMobile) {
            // Mobile behavior - toggle open/close
            if (this.sidebarOpen) {
                this.closeSidebar();
            } else {
                this.openSidebar();
            }
        } else {
            // Desktop behavior - toggle hide/show
            if (this.sidebarHiddenOnDesktop) {
                this.showSidebarOnDesktop();
            } else {
                this.hideSidebarOnDesktop();
            }
        }
    }

    openSidebar() {
        if (!this.isMobile) return;

        const sidebar = document.querySelector('.course-sidebar');
        const overlay = document.querySelector('.sidebar-overlay');
        const toggleButtons = document.querySelectorAll('.sidebar-toggle, .sidebar-hamburger-toggle');

        if (sidebar) {
            sidebar.classList.add('active');
            sidebar.setAttribute('aria-hidden', 'false');
            this.sidebarOpen = true;
        }

        if (overlay) {
            overlay.classList.add('active');
        }

        // Update button aria labels
        toggleButtons.forEach(btn => {
            btn.setAttribute('aria-label', 'Close sidebar');
        });

        // Prevent body scroll
        document.body.style.overflow = 'hidden';
    }

    closeSidebar() {
        if (!this.isMobile) return;

        const sidebar = document.querySelector('.course-sidebar');
        const overlay = document.querySelector('.sidebar-overlay');
        const toggleButtons = document.querySelectorAll('.sidebar-toggle, .sidebar-hamburger-toggle');

        if (sidebar) {
            sidebar.classList.remove('active');
            sidebar.setAttribute('aria-hidden', 'true');
            this.sidebarOpen = false;
        }

        if (overlay) {
            overlay.classList.remove('active');
        }

        // Update button aria labels
        toggleButtons.forEach(btn => {
            btn.setAttribute('aria-label', 'Open sidebar');
        });

        // Restore body scroll
        document.body.style.overflow = '';
    }

    hideSidebarOnDesktop() {
        if (this.isMobile) return;

        const sidebar = document.querySelector('.course-sidebar');
        const container = document.querySelector('.course-viewer-container');
        const toggleButtons = document.querySelectorAll('.sidebar-toggle, .sidebar-hamburger-toggle');

        if (sidebar) {
            sidebar.classList.add('desktop-hidden');
            sidebar.setAttribute('aria-hidden', 'true');
            this.sidebarHiddenOnDesktop = true;
        }

        if (container) {
            container.classList.add('sidebar-hidden');
        }

        // Update button aria labels
        toggleButtons.forEach(btn => {
            btn.setAttribute('aria-label', 'Show sidebar');
        });
    }

    showSidebarOnDesktop() {
        if (this.isMobile) return;

        const sidebar = document.querySelector('.course-sidebar');
        const container = document.querySelector('.course-viewer-container');
        const toggleButtons = document.querySelectorAll('.sidebar-toggle, .sidebar-hamburger-toggle');

        if (sidebar) {
            sidebar.classList.remove('desktop-hidden');
            sidebar.setAttribute('aria-hidden', 'false');
            this.sidebarHiddenOnDesktop = false;
        }

        if (container) {
            container.classList.remove('sidebar-hidden');
        }

        // Update button aria labels
        toggleButtons.forEach(btn => {
            btn.setAttribute('aria-label', 'Hide sidebar');
        });
    }

    toggleChapter(event) {
        event.preventDefault();
        const header = event.currentTarget;
        const chapter = header.closest('.chapter');
        const lecturesList = chapter.querySelector('.lectures-list');
        const toggle = header.querySelector('.chapter-toggle');
        
        if (chapter.classList.contains('expanded')) {
            // Collapse chapter
            chapter.classList.remove('expanded');
            lecturesList.style.maxHeight = '0';
            lecturesList.style.opacity = '0';
        } else {
            // Expand chapter
            chapter.classList.add('expanded');
            lecturesList.style.maxHeight = lecturesList.scrollHeight + 'px';
            lecturesList.style.opacity = '1';
        }
    }

    expandCurrentChapter() {
        // Find and expand the chapter containing the current lecture
        const currentLecture = document.querySelector('.lecture-item.active');
        if (currentLecture) {
            const chapter = currentLecture.closest('.chapter');
            if (chapter && !chapter.classList.contains('expanded')) {
                const header = chapter.querySelector('.chapter-header');
                if (header) {
                    this.toggleChapter({ currentTarget: header, preventDefault: () => {} });
                }
            }
        }
    }

    handleLectureClick(event) {
        event.preventDefault(); // Prevent default navigation
        
        const lectureItem = event.currentTarget;
        const lectureUrl = lectureItem.href;
        
        // Store current scroll position
        const sidebar = document.querySelector('.course-sidebar');
        const scrollPosition = sidebar ? sidebar.scrollTop : 0;
        
        // Add loading state
        lectureItem.style.opacity = '0.7';
        
        // Load lecture content via AJAX
        this.loadLectureContent(lectureUrl, scrollPosition)
            .then(() => {
                // Close sidebar after selection only on mobile
                if (this.isMobile) {
                    setTimeout(() => this.closeSidebar(), 300);
                }
            })
            .catch(error => {
                console.error('Error loading lecture:', error);
                // Fallback to normal navigation
                window.location.href = lectureUrl;
            })
            .finally(() => {
                lectureItem.style.opacity = '';
            });
    }

    handleNavigation(event) {
        const btn = event.currentTarget;
        
        // Add loading state
        btn.style.opacity = '0.7';
        btn.style.pointerEvents = 'none';
        
        // Navigation will be handled by the href, so we just need to show loading
        setTimeout(() => {
            btn.style.opacity = '';
            btn.style.pointerEvents = '';
        }, 1000);
    }

    async toggleLectureComplete(event) {
        event.preventDefault();

        if (!this.courseId || !this.currentLectureId || !this.csrfToken || !this.currentChapterSlug || !this.currentLectureSlug) {
            console.error('Missing required data:', {
                courseId: this.courseId,
                currentLectureId: this.currentLectureId,
                currentChapterSlug: this.currentChapterSlug,
                currentLectureSlug: this.currentLectureSlug,
                csrfToken: this.csrfToken ? 'present' : 'missing'
            });
            this.showNotification('Missing required data for completion toggle', 'error');
            return;
        }

        const completeBtn = event.currentTarget;
        const originalContent = completeBtn.innerHTML;
        const isCompleted = completeBtn.dataset.completed === 'true';
        const action = isCompleted ? 'uncomplete' : 'complete';
        
        // Show loading state
        completeBtn.innerHTML = `
            <svg class="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            <span>${isCompleted ? 'Uncompleting...' : 'Completing...'}</span>
        `;
        completeBtn.disabled = true;

        try {
            // Try to extract chapter slug from URL again if it's still missing
            if (!this.currentChapterSlug) {
                this.extractFromUrlIfNeeded();
                
                // If still missing after extraction attempt, use a fallback value
                if (!this.currentChapterSlug) {
                    console.warn('Chapter slug still missing after extraction attempt, using "chapter" as fallback');
                    this.currentChapterSlug = 'chapter'; // Fallback value
                }
            }
            
            console.log('Debug values:', {
                courseId: this.courseId,
                currentChapterSlug: this.currentChapterSlug,
                currentLectureSlug: this.currentLectureSlug,
                action: action
            });
            const url = `/my-courses/${this.courseId}/${this.currentChapterSlug}/lecture/${this.currentLectureSlug}/${action}`;
            console.log('Making request to:', url);

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': this.csrfToken,
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error('HTTP error response:', {
                    status: response.status,
                    statusText: response.statusText,
                    body: errorText
                });
                throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
            }

            const data = await response.json();
            console.log('Server response:', data);

            if (data.success) {
                this.updateProgressUI(data);
                this.updateLectureStatus(isCompleted);
                const message = isCompleted ? 'Lecture marked as uncomplete! 📝' : 'Lecture completed! 🎉';
                this.showNotification(message, 'success');
            } else {
                throw new Error(data.message || `Failed to ${action} lecture`);
            }

        } catch (error) {
            console.error(`Error ${action}ing lecture:`, error);
            this.showNotification(`An error occurred while ${action}ing the lecture`, 'error');
            
            // Restore button
            completeBtn.innerHTML = originalContent;
            completeBtn.disabled = false;
        }
    }

    updateProgressUI(data) {
        console.log('Updating progress UI with data:', data);

        // Update progress bar
        const progressBar = document.querySelector('.progress-bar');
        if (progressBar) {
            progressBar.style.width = data.progress + '%';
            console.log('Updated progress bar to:', data.progress + '%');
        } else {
            console.error('Progress bar element not found');
        }

        // Update progress text
        const progressText = document.querySelector('.progress-percentage');
        if (progressText) {
            progressText.textContent = data.progress.toFixed(1) + '%';
            console.log('Updated progress text to:', data.progress.toFixed(1) + '%');
        } else {
            console.error('Progress percentage element not found');
        }

        // Update lecture count
        const lectureCount = document.querySelector('.lecture-count');
        if (lectureCount) {
            lectureCount.textContent = `${data.completed_lectures}/${data.total_lectures} lessons`;
            console.log('Updated lecture count to:', `${data.completed_lectures}/${data.total_lectures} lessons`);
        } else {
            console.error('Lecture count element not found');
        }
    }

    updateSidebarStates() {
        console.log('Updating sidebar states for current lecture:', this.currentLectureId);
        
        // Remove active state from all lecture items
        document.querySelectorAll('.lecture-item').forEach(item => {
            item.classList.remove('active');
        });
        
        // Add active state to current lecture
        const currentLectureItem = document.querySelector(`.lecture-item[data-lecture-id="${this.currentLectureId}"]`);
        if (currentLectureItem) {
            // Only add active class if it's not already completed
            if (!currentLectureItem.classList.contains('completed')) {
                currentLectureItem.classList.add('active');
            }
            
            // Update the status icon for current lecture
            const statusIcon = currentLectureItem.querySelector('.lecture-status');
            if (statusIcon && !currentLectureItem.classList.contains('completed')) {
                statusIcon.className = 'lecture-status current';
                statusIcon.innerHTML = `
                    <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                    </svg>
                `;
            }
        } else {
            console.error('Current lecture item not found with selector:', `.lecture-item[data-lecture-id="${this.currentLectureId}"]`);
        }
    }

    updateLectureStatus(wasCompleted = false) {
        console.log('Updating lecture status for lecture ID:', this.currentLectureId, 'wasCompleted:', wasCompleted);

        // Update current lecture in sidebar
        const currentLectureItem = document.querySelector(`.lecture-item[data-lecture-id="${this.currentLectureId}"]`);
        if (currentLectureItem) {
            if (wasCompleted) {
                // Was completed, now uncompleting
                currentLectureItem.classList.remove('completed');
                currentLectureItem.classList.add('active');
            } else {
                // Was uncompleted, now completing
                currentLectureItem.classList.remove('active');
                currentLectureItem.classList.add('completed');
            }
            console.log('Updated lecture item classes');

            // Update status icon
            const statusIcon = currentLectureItem.querySelector('.lecture-status');
            if (statusIcon) {
                if (wasCompleted) {
                    // Show current/play icon (was completed, now uncompleting)
                    statusIcon.className = 'lecture-status current';
                    statusIcon.innerHTML = `
                        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                        </svg>
                    `;
                } else {
                    // Show completed/checkmark icon (was uncompleted, now completing)
                    statusIcon.className = 'lecture-status completed';
                    statusIcon.innerHTML = `
                        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    `;
                }
                console.log('Updated status icon');
            } else {
                console.error('Status icon not found');
            }
        } else {
            console.error('Current lecture item not found with selector:', `.lecture-item[data-lecture-id="${this.currentLectureId}"]`);
        }

        // Update complete button
        const completeBtn = document.querySelector('.complete-btn');
        if (completeBtn) {
            if (wasCompleted) {
                // Was completed, now show mark as complete button
                completeBtn.innerHTML = `
                    <i class="fas fa-check"></i>
                    <span>Mark as Complete</span>
                `;
                completeBtn.classList.remove('completed');
                completeBtn.dataset.completed = 'false';
                completeBtn.disabled = false;
            } else {
                // Was uncompleted, now show mark as uncomplete button
                completeBtn.innerHTML = `
                    <i class="fas fa-undo"></i>
                    <span>Mark as Uncomplete</span>
                `;
                completeBtn.classList.add('completed');
                completeBtn.dataset.completed = 'true';
                completeBtn.disabled = false;
            }
            console.log('Updated complete button state');
        } else {
            console.error('Complete button not found');
        }
    }



    async loadLectureContent(url, scrollPosition = 0) {
        try {
            // Add AJAX header to request
            const response = await fetch(url, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'text/html'
                }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const html = await response.text();
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            
            // Update the main content area
            const newContent = doc.querySelector('.course-content');
            const currentContent = document.querySelector('.course-content');
            
            if (newContent && currentContent) {
                currentContent.innerHTML = newContent.innerHTML;
            }
            
            // Update sidebar states dynamically instead of replacing content
            this.updateSidebarStates();
            
            // Restore scroll position
            const sidebar = document.querySelector('.course-sidebar');
            if (sidebar) {
                sidebar.scrollTop = scrollPosition;
            }
            
            // Update page title and meta tags
            const newTitle = doc.querySelector('title');
            if (newTitle) {
                document.title = newTitle.textContent;
            }
            
            // Update meta tags
            const metaTags = ['course-id', 'current-lecture-id', 'current-lecture-slug', 'current-chapter-slug'];
            metaTags.forEach(name => {
                const newMeta = doc.querySelector(`meta[name="${name}"]`);
                const currentMeta = document.querySelector(`meta[name="${name}"]`);
                if (newMeta && currentMeta) {
                    currentMeta.setAttribute('content', newMeta.getAttribute('content'));
                }
            });
            
            // Update browser URL without reload
            window.history.pushState({}, '', url);
            
            // Re-read meta tags to update object properties
            this.readMetaTags();
            
            // Update sidebar states again after meta tags are updated
            this.updateSidebarStates();
            
            // Re-initialize course viewer with new data
            this.initializeSidebar();
            this.expandCurrentChapter();
            
            // Re-bind complete button event
            const completeBtn = document.querySelector('.complete-btn');
            if (completeBtn) {
                completeBtn.addEventListener('click', (e) => this.toggleLectureComplete(e));
            }
            
            // Re-bind navigation buttons
            document.querySelectorAll('.nav-btn').forEach(btn => {
                btn.addEventListener('click', (e) => this.handleNavigation(e));
            });
            
        } catch (error) {
            console.error('Error loading lecture content:', error);
            throw error;
        }
    }
    
    handleNavigation(event) {
        event.preventDefault();
        
        const navBtn = event.currentTarget;
        const navUrl = navBtn.href;
        
        if (!navUrl) return;
        
        // Store current scroll position
        const sidebar = document.querySelector('.course-sidebar');
        const scrollPosition = sidebar ? sidebar.scrollTop : 0;
        
        // Add loading state
        navBtn.style.opacity = '0.7';
        
        // Load lecture content via AJAX
        this.loadLectureContent(navUrl, scrollPosition)
            .catch(error => {
                console.error('Error loading lecture:', error);
                // Fallback to normal navigation
                window.location.href = navUrl;
            })
            .finally(() => {
                navBtn.style.opacity = '';
            });
    }
    
    bindSidebarEvents() {
        // Re-bind chapter toggles
        document.querySelectorAll('.chapter-header').forEach(header => {
            header.addEventListener('click', (e) => this.toggleChapter(e));
        });
        
        // Re-bind lecture navigation
        document.querySelectorAll('.lecture-item').forEach(item => {
            item.addEventListener('click', (e) => this.handleLectureClick(e));
        });
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Only handle shortcuts when not in input fields
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                return;
            }

            switch (e.code) {
                case 'Space':
                    e.preventDefault();
                    const completeBtn = document.querySelector('.complete-btn:not(.completed)');
                    if (completeBtn) {
                        completeBtn.click();
                    }
                    break;
                    
                case 'ArrowLeft':
                    e.preventDefault();
                    const prevBtn = document.querySelector('.nav-btn[href*="lecture/"]:first-of-type');
                    if (prevBtn) {
                        this.handleNavigation({ currentTarget: prevBtn, preventDefault: () => {} });
                    }
                    break;
                    
                case 'ArrowRight':
                    e.preventDefault();
                    const nextBtn = document.querySelector('.nav-btn[href*="lecture/"]:last-of-type');
                    if (nextBtn) {
                        this.handleNavigation({ currentTarget: nextBtn, preventDefault: () => {} });
                    }
                    break;
                    
                case 'KeyS':
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        this.toggleSidebar();
                    }
                    break;

                case 'KeyB':
                    // Toggle sidebar with 'B' key (for sidebar/bar)
                    e.preventDefault();
                    this.toggleSidebar();
                    break;
            }
        });
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span>${message}</span>
                <button class="notification-close">&times;</button>
            </div>
        `;

        // Add styles
        notification.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            z-index: 9999;
            padding: 1rem 1.5rem;
            border-radius: 0.5rem;
            color: white;
            font-weight: 500;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
            transform: translateX(100%);
            transition: transform 0.3s ease;
            max-width: 400px;
        `;

        // Set background color based on type
        const colors = {
            success: '#10b981',
            error: '#ef4444',
            warning: '#f59e0b',
            info: '#3b82f6'
        };
        notification.style.backgroundColor = colors[type] || colors.info;

        // Add to DOM
        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Auto remove
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 5000);

        // Close button
        const closeBtn = notification.querySelector('.notification-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            });
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new CourseViewer();
});

// Export for potential external use
window.CourseViewer = CourseViewer;
